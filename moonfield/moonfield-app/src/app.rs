use std::ptr;
use std::sync::Arc;

use winit::application::ApplicationHandler;
use winit::event::WindowEvent;
use winit::event_loop::ActiveEventLoop;
use winit::window::{Window, WindowId};

use std::ffi::CString;
use ash::{vk, extensions::{khr::Surface, khr::XlibSurface, ext::DebugUtils}};

pub struct App {
    window: Option<Arc<Window>>,

    _entry: ash::Entry,
    instance: ash::Instance,
}

impl App {
    pub fn new() -> Self {
        let entry = unsafe { ash::Entry::load().unwrap() };
        let instance = Self::create_instance(&entry);
        Self {
            _entry: entry,
            instance,
            window: None,
        }
    }

    pub fn create_instance(entry: &ash::Entry) -> ash::Instance {
        let app_name = CString::new("Moonfield App").unwrap();
        let engine_name = CString::new("Moonfield Engine").unwrap();

        let app_info = vk::ApplicationInfo {
            s_type: vk::StructureType::APPLICATION_INFO,
            p_next: std::ptr::null(),
            p_application_name: app_name.as_ptr(),
            application_version: vk::make_version(1, 3, 0),
            p_engine_name: engine_name.as_ptr(),
            engine_version: vk::make_version(1, 3, 0),
            api_version: vk::make_version(1, 3, 0),
        };

        let extension_names =  vec![
            Surface::name().as_ptr(),
            XlibSurface::name().as_ptr(),
            DebugUtils::name().as_ptr(),
        ];

        let create_info = vk::InstanceCreateInfo {
            s_type: vk::StructureType::INSTANCE_CREATE_INFO,
            p_next: ptr::null(),
            flags: vk::InstanceCreateFlags::empty(),
            p_application_info: &app_info,
            pp_enabled_layer_names: ptr::null(),
            enabled_layer_count: 0,
            pp_enabled_extension_names: extension_names.as_ptr(),
            enabled_extension_count: extension_names.len() as u32,
        };

        let instance : ash::Instance = unsafe {
            entry.create_instance(&create_info, None).expect("Failed to create Instance!")
        };

        instance
    }

    pub fn draw_frame(&self) {

    }
}

impl ApplicationHandler for App {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.window.is_none() {
            let win_attr = Window::default_attributes().with_title("winit example");
            let window = Arc::new(
                event_loop
                    .create_window(win_attr)
                    .expect("create window err."),
            );
            self.window = Some(window);
        }
    }

    fn window_event(
        &mut self,
        event_loop: &ActiveEventLoop,
        _window_id: WindowId,
        event: WindowEvent,
    ) {
        match event {
            WindowEvent::CloseRequested => {
                
                event_loop.exit();
            }
            WindowEvent::Resized(new_size) =>{
                
            }
            WindowEvent::RedrawRequested => {
                self.draw_frame();
            }
            _ => (),
        }
    }
}

impl Drop for App {
    fn drop(&mut self) {
        unsafe {
            self.instance.destroy_instance(None);
        }
    }
}