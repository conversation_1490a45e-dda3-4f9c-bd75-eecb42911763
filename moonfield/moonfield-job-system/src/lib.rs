//! Multi-threaded job system for the Moonfield engine.
//!
//! This crate provides a high-performance job system for parallel task execution,
//! featuring work-stealing queues and efficient thread management.

pub mod job;
pub mod job_system;
pub mod work_stealing_dequeue;

/// Currently rust do not support const as attributes
pub const CACHELINE_SIZE: usize = 64; 

pub const MAX_JOB_COUNT: usize = 1 << 14; // 16384
pub const JOB_COUNT_MASK: u32 = (MAX_JOB_COUNT - 1) as u32;

pub const WAITER_COUNT_SHIFT: u32 = 24;

pub type ThreadId = u8;
pub const INVALID_THREAD_ID: u8 = 0xff;
