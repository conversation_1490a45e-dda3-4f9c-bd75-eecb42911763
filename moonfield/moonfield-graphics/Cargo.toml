[package]
name = "moonfield-graphics"
version = "0.1.0"
edition = "2021"
description = "Graphics backend abstraction for the Moonfield engine"
license = "MIT OR Apache-2.0"

[dependencies]
moonfield-allocator = { path = "../moonfield-allocator" }
ash = { workspace = true }
ash-window = { workspace = true }
raw-window-handle = { workspace = true }
gpu-allocator = { workspace = true }
bytemuck = { workspace = true }
tracing = { workspace = true }
anyhow = { workspace = true }
winit = { workspace = true }

[features]
default = ["vulkan"]
vulkan = []
opengl = []
webgpu = []
