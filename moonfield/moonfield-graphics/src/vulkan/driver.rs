/*
use crate::backend::vulkan::context::VulkanContext;
use crate::backend::vulkan::platform::{QueueFamiliesIndices, VulkanPlatform};
use crate::backend::vulkan::swapchain::SwapchainSupportDetails;
use ash::{ext::debug_utils, khr::surface, vk, Device, Entry, Instance};
use std::ffi::CStr;
use std::sync::Arc;
use tracing::debug;
use winit::window::Window;

/// VulkanDriver is the top-level interface for Vulkan operations.
/// It wraps the platform and context functionality and controls debugging.
pub struct VulkanDriver {
    // Vulkan resources
    instance: Option<Arc<Instance>>,
    physical_device: Option<vk::PhysicalDevice>,
    device: Option<Device>,
    surface: Option<surface::Instance>,
    surface_khr: Option<vk::SurfaceKHR>,
    graphics_queue: Option<vk::Queue>,
    present_queue: Option<vk::Queue>,
    queue_families_indices: Option<QueueFamiliesIndices>,

    // Context
    context: Option<VulkanContext>,

    // Debug
    debug_utils: Option<debug_utils::Instance>,
    debug_messenger: Option<vk::DebugUtilsMessengerEXT>,
    enable_validation: bool,
}

impl VulkanDriver {
    /// Create a new VulkanDriver with the given window.
    pub unsafe fn new(window: &Window) -> Self {
        debug!("Creating Vulkan driver.");

        // Create the platform
        let platform = VulkanPlatform::new(window);

        // Debug validation is enabled in debug builds by default
        let enable_validation = cfg!(debug_assertions);

        // Setup debug messenger if validation layers are enabled
        let (debug_utils, debug_messenger) = if enable_validation {
            unsafe { Self::setup_debug_messenger(platform.instance()) }
        } else {
            (None, None)
        };

        // Extract resources from platform
        let instance = Some(Arc::new(platform.instance().clone()));
        let physical_device = Some(platform.physical_device());
        let device = Some(platform.device().clone());
        let surface = Some(platform.surface().clone());
        let surface_khr = Some(platform.surface_khr());
        let graphics_queue = Some(platform.graphics_queue());
        let present_queue = Some(platform.present_queue());
        let queue_families_indices = Some(platform.queue_families_indices().clone());
        let context = Some(VulkanContext::new(Arc::new(platform.instance().clone()), platform.physical_device()));

        VulkanDriver {
            instance,
            physical_device,
            device,
            surface,
            surface_khr,
            graphics_queue,
            present_queue,
            queue_families_indices,
            context,
            debug_utils,
            debug_messenger,
            enable_validation,
        }
    }

    /// Create a new VulkanDriver without a window.
    /// This is used by the DriverFactory.
    pub fn new_without_window() -> Self {
        debug!("Creating Vulkan driver without window.");

        // Create a default driver
        // The resources will be set later when a window is available
        VulkanDriver {
            instance: None,
            physical_device: None,
            device: None,
            surface: None,
            surface_khr: None,
            graphics_queue: None,
            present_queue: None,
            queue_families_indices: None,
            context: None,
            debug_utils: None,
            debug_messenger: None,
            enable_validation: cfg!(debug_assertions),
        }
    }

    /// Setup debug messenger for validation layers
    unsafe fn setup_debug_messenger(
        instance: &Instance,
    ) -> (
        Option<debug_utils::Instance>,
        Option<vk::DebugUtilsMessengerEXT>,
    ) {
        let debug_utils = debug_utils::Instance::new(&unsafe { Entry::load() }.unwrap(), instance);

        let create_info = vk::DebugUtilsMessengerCreateInfoEXT::default()
            .flags(vk::DebugUtilsMessengerCreateFlagsEXT::empty())
            .message_severity(
                vk::DebugUtilsMessageSeverityFlagsEXT::ERROR
                    | vk::DebugUtilsMessageSeverityFlagsEXT::WARNING
                    | vk::DebugUtilsMessageSeverityFlagsEXT::INFO,
            )
            .message_type(
                vk::DebugUtilsMessageTypeFlagsEXT::GENERAL
                    | vk::DebugUtilsMessageTypeFlagsEXT::VALIDATION
                    | vk::DebugUtilsMessageTypeFlagsEXT::PERFORMANCE,
            )
            .pfn_user_callback(Some(Self::vulkan_debug_callback));

        let debug_messenger = unsafe {
            debug_utils
                .create_debug_utils_messenger(&create_info, None)
                .unwrap()
        };

        (Some(debug_utils), Some(debug_messenger))
    }

    /// Debug callback function for Vulkan validation layers
    unsafe extern "system" fn vulkan_debug_callback(
        flag: vk::DebugUtilsMessageSeverityFlagsEXT,
        typ: vk::DebugUtilsMessageTypeFlagsEXT,
        p_callback_data: *const vk::DebugUtilsMessengerCallbackDataEXT,
        _: *mut std::os::raw::c_void,
    ) -> vk::Bool32 {
        use vk::DebugUtilsMessageSeverityFlagsEXT as Flag;

        let message = unsafe { CStr::from_ptr((*p_callback_data).p_message) };
        match flag {
            Flag::VERBOSE => debug!("{:?} - {:?}", typ, message),
            Flag::INFO => tracing::info!("{:?} - {:?}", typ, message),
            Flag::WARNING => tracing::warn!("{:?} - {:?}", typ, message),
            _ => tracing::error!("{:?} - {:?}", typ, message),
        }
        vk::FALSE
    }

    /// Enable or disable validation layers.
    /// This setting will be applied on the next instance creation.
    pub fn set_validation_enabled(&mut self, enabled: bool) {
        self.enable_validation = enabled;
    }

    /// Check if validation layers are enabled.
    pub fn is_validation_enabled(&self) -> bool {
        self.enable_validation
    }

    /// Wait for the device to be idle.
    pub fn wait_idle(&self) {
        if let Some(device) = &self.device {
            unsafe { device.device_wait_idle().unwrap() };
        }
    }

    /// Get a reference to the Vulkan context.
    pub fn context(&self) -> Option<&VulkanContext> {
        self.context.as_ref()
    }

    /// Get a reference to the Vulkan instance.
    pub fn instance(&self) -> Option<&Instance> {
        self.instance.as_ref().map(|rc| rc.as_ref())
    }

    /// Get a reference to the Vulkan device.
    pub fn device(&self) -> Option<&Device> {
        self.device.as_ref()
    }

    /// Get the physical device.
    pub fn physical_device(&self) -> Option<vk::PhysicalDevice> {
        self.physical_device
    }

    /// Get the graphics queue.
    pub fn graphics_queue(&self) -> Option<vk::Queue> {
        self.graphics_queue
    }

    /// Get the present queue.
    pub fn present_queue(&self) -> Option<vk::Queue> {
        self.present_queue
    }

    /// Get the surface.
    pub fn surface(&self) -> Option<&surface::Instance> {
        self.surface.as_ref()
    }

    /// Get the surface KHR.
    pub fn surface_khr(&self) -> Option<vk::SurfaceKHR> {
        self.surface_khr
    }

    /// Get queue families indices.
    pub fn queue_families_indices(&self) -> Option<&QueueFamiliesIndices> {
        self.queue_families_indices.as_ref()
    }

    /// Get swapchain support details.
    pub fn get_swapchain_support_details(&self) -> Option<SwapchainSupportDetails> {
        match (self.physical_device, self.instance.as_ref(), self.surface_khr) {
            (Some(physical_device), Some(instance), Some(surface_khr)) => {
                Some(SwapchainSupportDetails::new(physical_device, instance, surface_khr))
            }
            _ => None,
        }
    }

    /// 获取命令池
    pub fn get_command_pool(&self) -> vk::CommandPool {
        // 在实际应用中，应该创建和管理命令池
        // 这里为了简化，返回一个默认值
        vk::CommandPool::null()
    }

    /// 获取传输队列
    pub fn get_transfer_queue(&self) -> vk::Queue {
        // 在实际应用中，应该返回专用的传输队列
        // 这里为了简化，返回图形队列
        self.graphics_queue.unwrap_or(vk::Queue::null())
    }

    /// Push a debug label for the current command buffer.
    /// This is a no-op if debug markers are not supported.
    pub fn push_debug_label(&self, _command_buffer: vk::CommandBuffer, _label: &str) {
        // This will be implemented to push debug labels
        // For now, it's a placeholder
        if let Some(context) = self.context() {
            if context.is_debug_marker_supported() {
                // Push debug label
            }
        }
    }

    /// Pop a debug label for the current command buffer.
    /// This is a no-op if debug markers are not supported.
    pub fn pop_debug_label(&self, _command_buffer: vk::CommandBuffer) {
        // This will be implemented to pop debug labels
        // For now, it's a placeholder
        if let Some(context) = self.context() {
            if context.is_debug_marker_supported() {
                // Pop debug label
            }
        }
    }

    /// Insert a debug label for the current command buffer.
    /// This is a no-op if debug markers are not supported.
    pub fn insert_debug_label(&self, _command_buffer: vk::CommandBuffer, _label: &str) {
        // This will be implemented to insert debug labels
        // For now, it's a placeholder
        if let Some(context) = self.context() {
            if context.is_debug_marker_supported() {
                // Insert debug label
            }
        }
    }
}

impl Drop for VulkanDriver {
    fn drop(&mut self) {
        debug!("Dropping Vulkan driver.");

        // 在销毁任何资源之前等待设备空闲
        // 这是确保所有GPU操作完成的关键步骤
        if let Some(device) = &self.device {
            unsafe {
                if let Err(err) = device.device_wait_idle() {
                    debug!("Failed to wait for device to be idle: {:?}", err);
                }
            }
        }

        // Clean up debug messenger if it exists
        if let (Some(utils), Some(messenger)) =
            (self.debug_utils.take(), self.debug_messenger.take())
        {
            unsafe {
                utils.destroy_debug_utils_messenger(messenger, None);
            }
        }

        // 注意：不要在这里销毁device、surface等资源
        // 这些资源应该由VulkanPlatform的Drop实现来销毁
        // 否则可能会导致重复销毁或销毁顺序错误
    }
}
*/
