/*
use crate::backend::vulkan::context::VulkanContext;
use crate::backend::vulkan::swapchain::SwapchainSupportDetails;
use ash::{
    ext::debug_utils, khr::{surface, swapchain as khr_swapchain}, vk,
    Device,
    Entry,
    Instance,
};
use raw_window_handle::{HasDisplayHandle, HasWindowHandle};
use std::any::Any;
use std::ffi::{CStr, CString};
use std::sync::Arc;
use tracing::debug;
use winit::window::Window;

// Constants for validation layers
const ENABLE_VALIDATION_LAYERS: bool = cfg!(debug_assertions);
const REQUIRED_LAYERS: [&str; 1] = ["VK_LAYER_KHRONOS_validation"];

pub fn get_required_device_extensions() -> [&'static CStr; 1] {
    [khr_swapchain::NAME]
}

#[derive(Debug, Clone)]
pub struct QueueFamiliesIndices {
    pub graphics_family: Option<u32>,
    pub present_family: Option<u32>,
}

pub struct VulkanPlatform {
    entry: Entry,
    instance: Arc<Instance>,
    surface: surface::Instance, // instance level surface related functions wrapper
    surface_khr: vk::SurfaceKHR, // actual surface handle
    physical_device: vk::PhysicalDevice,
    device: Device,
    graphics_queue: vk::Queue,
    present_queue: vk::Queue,
    context: VulkanContext,
    queue_families_indices: QueueFamiliesIndices,
}

impl VulkanPlatform {
    pub fn new(window: &Window) -> Self {
        debug!("Creating Vulkan platform.");

        let entry = unsafe { Entry::load().expect("Failed to create entry.") };
        let instance = Arc::new(Self::create_instance(&entry, window));

        // Create surface
        let surface = surface::Instance::new(&entry, &instance);
        let surface_khr = unsafe {
            ash_window::create_surface(
                &entry,
                instance.as_ref(),
                window.display_handle().unwrap().as_raw(),
                window.window_handle().unwrap().as_raw(),
                None,
            )
            .unwrap()
        };

        // Pick physical device and find queue families
        let (physical_device, queue_families_indices) =
            Self::pick_physical_device(instance.as_ref(), &surface, surface_khr);

        // Create logical device and get queues
        let (device, graphics_queue, present_queue) =
            Self::create_logical_device_with_graphics_queue(
                instance.as_ref(),
                physical_device,
                &queue_families_indices,
            );

        // Create Vulkan context
        let context = VulkanContext::new(Arc::clone(&instance), physical_device);

        VulkanPlatform {
            entry,
            instance,
            surface,
            surface_khr,
            physical_device,
            device,
            graphics_queue,
            present_queue,
            context,
            queue_families_indices,
        }
    }

    /// Create a new VulkanPlatform from a generic window.
    /// This is used by the PlatformFactory.
    pub fn new_from_any(window: &dyn Any) -> Result<Self, String> {
        // 将窗口转换为winit::window::Window
        let window = match window.downcast_ref::<Window>() {
            Some(window) => window,
            None => return Err("Window is not a winit::window::Window".to_string()),
        };

        // 创建平台
        Ok(Self::new(window))
    }

    pub fn wait_idle(&self) {
        unsafe { self.device.device_wait_idle().unwrap() };
    }

    fn create_instance(entry: &Entry, window: &Window) -> Instance {
        let app_name = CString::new("Moonfield").unwrap();
        let engine_name = CString::new("Moonfield").unwrap();
        let app_info = vk::ApplicationInfo::default()
            .application_name(app_name.as_c_str())
            .application_version(vk::API_VERSION_1_3)
            .engine_name(engine_name.as_c_str())
            .engine_version(vk::API_VERSION_1_3)
            .api_version(vk::API_VERSION_1_3);

        let extension_names =
            ash_window::enumerate_required_extensions(window.display_handle().unwrap().as_raw())
                .unwrap();
        let mut extension_names = extension_names.to_vec();
        if ENABLE_VALIDATION_LAYERS {
            extension_names.push(debug_utils::NAME.as_ptr());
        }

        let (_layer_names, layer_names_ptrs) = Self::get_layer_names_and_pointers();

        let create_flags = vk::InstanceCreateFlags::default();

        let mut debug_create_info = Self::create_debug_create_info();
        let mut instance_create_info = vk::InstanceCreateInfo::default()
            .application_info(&app_info)
            .enabled_extension_names(&extension_names)
            .flags(create_flags);
        if ENABLE_VALIDATION_LAYERS {
            Self::check_validation_layer_support(entry);
            instance_create_info = instance_create_info
                .enabled_layer_names(&layer_names_ptrs)
                .push_next(&mut debug_create_info);
        }

        unsafe { entry.create_instance(&instance_create_info, None).unwrap() }
    }

    fn setup_debug_messenger(
        entry: &Entry,
        instance: &Instance,
    ) -> Option<(debug_utils::Instance, vk::DebugUtilsMessengerEXT)> {
        if !ENABLE_VALIDATION_LAYERS {
            return None;
        }

        let create_info = Self::create_debug_create_info();
        let debug_utils = debug_utils::Instance::new(entry, instance);
        let debug_utils_messenger = unsafe {
            debug_utils
                .create_debug_utils_messenger(&create_info, None)
                .unwrap()
        };

        Some((debug_utils, debug_utils_messenger))
    }

    fn create_debug_create_info() -> vk::DebugUtilsMessengerCreateInfoEXT<'static> {
        vk::DebugUtilsMessengerCreateInfoEXT::default()
            .flags(vk::DebugUtilsMessengerCreateFlagsEXT::empty())
            .message_severity(
                vk::DebugUtilsMessageSeverityFlagsEXT::ERROR
                    | vk::DebugUtilsMessageSeverityFlagsEXT::WARNING
                    | vk::DebugUtilsMessageSeverityFlagsEXT::INFO,
            )
            .message_type(
                vk::DebugUtilsMessageTypeFlagsEXT::GENERAL
                    | vk::DebugUtilsMessageTypeFlagsEXT::VALIDATION
                    | vk::DebugUtilsMessageTypeFlagsEXT::PERFORMANCE,
            )
            .pfn_user_callback(Some(Self::vulkan_debug_callback))
    }

    unsafe extern "system" fn vulkan_debug_callback(
        flag: vk::DebugUtilsMessageSeverityFlagsEXT,
        typ: vk::DebugUtilsMessageTypeFlagsEXT,
        p_callback_data: *const vk::DebugUtilsMessengerCallbackDataEXT,
        _: *mut std::os::raw::c_void,
    ) -> vk::Bool32 {
        use vk::DebugUtilsMessageSeverityFlagsEXT as Flag;

        let message = unsafe { CStr::from_ptr((*p_callback_data).p_message) };
        match flag {
            Flag::VERBOSE => debug!("{:?} - {:?}", typ, message),
            Flag::INFO => tracing::info!("{:?} - {:?}", typ, message),
            Flag::WARNING => tracing::warn!("{:?} - {:?}", typ, message),
            _ => tracing::error!("{:?} - {:?}", typ, message),
        }
        vk::FALSE
    }

    fn get_layer_names_and_pointers() -> (Vec<CString>, Vec<*const std::os::raw::c_char>) {
        let layer_names = REQUIRED_LAYERS
            .iter()
            .map(|name| CString::new(*name).unwrap())
            .collect::<Vec<_>>();
        let layer_names_ptrs = layer_names
            .iter()
            .map(|name| name.as_ptr())
            .collect::<Vec<_>>();
        (layer_names, layer_names_ptrs)
    }

    fn check_validation_layer_support(entry: &Entry) {
        let supported_layers = unsafe { entry.enumerate_instance_layer_properties().unwrap() };
        for required in REQUIRED_LAYERS.iter() {
            let found = supported_layers.iter().any(|layer| {
                let name = unsafe { CStr::from_ptr(layer.layer_name.as_ptr()) };
                let name = name.to_str().expect("Failed to get layer name pointer");
                required == &name
            });

            if !found {
                panic!("Validation layer not supported: {}", required);
            }
        }
    }

    /// Pick the first suitable physical device.
    ///
    /// # Requirements
    /// - At least one queue family with one queue supportting graphics.
    /// - At least one queue family with one queue supporting presentation to `surface_khr`.
    /// - Swapchain extension support.
    ///
    /// # Returns
    ///
    /// A tuple containing the physical device and the queue families indices.
    fn pick_physical_device(
        instance: &Instance,
        surface: &surface::Instance,
        surface_khr: vk::SurfaceKHR,
    ) -> (vk::PhysicalDevice, QueueFamiliesIndices) {
        let devices = unsafe { instance.enumerate_physical_devices().unwrap() };
        let device = devices
            .into_iter()
            .find(|device| Self::is_device_suitable(instance, surface, surface_khr, *device))
            .expect("No suitable physical device.");

        let (graphics, present) = Self::find_queue_families(instance, surface, surface_khr, device);
        let indices = QueueFamiliesIndices {
            graphics_family: graphics,
            present_family: present,
        };

        (device, indices)
    }

    fn is_device_suitable(
        instance: &Instance,
        surface: &surface::Instance,
        surface_khr: vk::SurfaceKHR,
        device: vk::PhysicalDevice,
    ) -> bool {
        let (graphics, present) = Self::find_queue_families(instance, surface, surface_khr, device);
        let extention_support = Self::check_device_extension_support(instance, device);
        let is_swapchain_adequate = {
            let details = Self::get_swapchain_support(device, surface, surface_khr);
            !details.formats.is_empty() && !details.present_modes.is_empty()
        };
        let features = unsafe { instance.get_physical_device_features(device) };
        graphics.is_some()
            && present.is_some()
            && extention_support
            && is_swapchain_adequate
            && features.sampler_anisotropy == vk::TRUE
    }

    pub fn check_device_extension_support(instance: &Instance, device: vk::PhysicalDevice) -> bool {
        let required_extentions = Self::get_required_device_extensions();

        let extension_props = unsafe {
            instance
                .enumerate_device_extension_properties(device)
                .unwrap()
        };

        for required in required_extentions.iter() {
            let found = extension_props.iter().any(|ext| {
                let name = unsafe { CStr::from_ptr(ext.extension_name.as_ptr()) };
                required == &name
            });

            if !found {
                return false;
            }
        }

        true
    }

    pub fn get_required_device_extensions() -> [&'static CStr; 1] {
        [khr_swapchain::NAME]
    }

    /// Find a queue family with at least one graphics queue and one with
    /// at least one presentation queue from `device`.
    ///
    /// #Returns
    ///
    /// Return a tuple (Option<graphics_family_index>, Option<present_family_index>).
    fn find_queue_families(
        instance: &Instance,
        surface: &surface::Instance,
        surface_khr: vk::SurfaceKHR,
        device: vk::PhysicalDevice,
    ) -> (Option<u32>, Option<u32>) {
        let mut graphics = None;
        let mut present = None;

        let queue_families =
            unsafe { instance.get_physical_device_queue_family_properties(device) };

        for (i, family) in queue_families.iter().enumerate() {
            if family.queue_flags.contains(vk::QueueFlags::GRAPHICS) {
                graphics = Some(i as u32);
            }

            let is_present_support = unsafe {
                surface
                    .get_physical_device_surface_support(device, i as u32, surface_khr)
                    .unwrap()
            };

            if is_present_support {
                present = Some(i as u32);
            }

            if graphics.is_some() && present.is_some() {
                break;
            }
        }

        (graphics, present)
    }

    fn create_logical_device_with_graphics_queue(
        instance: &Instance,
        physical_device: vk::PhysicalDevice,
        queue_families_indices: &QueueFamiliesIndices,
    ) -> (Device, vk::Queue, vk::Queue) {
        let graphics_family_index = queue_families_indices.graphics_family.unwrap();
        let present_family_index = queue_families_indices.present_family.unwrap();
        let queue_priorities = [1.0_f32];

        let queue_create_infos = {
            // Vulkan specs does not allow passing an array containing duplicated family indices.
            // And since the family for graphics and presentation could be the same we need to
            // deduplicate it.
            let mut indices = vec![graphics_family_index, present_family_index];
            indices.dedup();

            // Now we build an array of `DeviceQueueCreateInfo`.
            // One for each different family index.
            indices
                .iter()
                .map(|index| {
                    vk::DeviceQueueCreateInfo::default()
                        .queue_family_index(*index)
                        .queue_priorities(&queue_priorities)
                })
                .collect::<Vec<_>>()
        };

        let device_extensions = Self::get_required_device_extensions();
        let device_extensions_ptrs = device_extensions
            .iter()
            .map(|ext| ext.as_ptr())
            .collect::<Vec<_>>();

        let device_features = vk::PhysicalDeviceFeatures::default().sampler_anisotropy(true);

        let device_create_info = vk::DeviceCreateInfo::default()
            .queue_create_infos(&queue_create_infos)
            .enabled_extension_names(&device_extensions_ptrs)
            .enabled_features(&device_features);

        // Build device and queues
        let device = unsafe {
            instance
                .create_device(physical_device, &device_create_info, None)
                .expect("Failed to create logical device.")
        };
        let graphics_queue = unsafe { device.get_device_queue(graphics_family_index, 0) };
        let present_queue = unsafe { device.get_device_queue(present_family_index, 0) };

        (device, graphics_queue, present_queue)
    }

    pub fn get_swapchain_support_details(
        &self,
        device: vk::PhysicalDevice,
        surface: &surface::Instance,
        surface_khr: vk::SurfaceKHR,
    ) -> SwapchainSupportDetails {
        Self::get_swapchain_support(device, surface, surface_khr)
    }

    fn get_swapchain_support(
        device: vk::PhysicalDevice,
        surface: &surface::Instance,
        surface_khr: vk::SurfaceKHR,
    ) -> SwapchainSupportDetails {
        let capabilities = unsafe {
            surface
                .get_physical_device_surface_capabilities(device, surface_khr)
                .unwrap()
        };

        let formats = unsafe {
            surface
                .get_physical_device_surface_formats(device, surface_khr)
                .unwrap()
        };

        let present_modes = unsafe {
            surface
                .get_physical_device_surface_present_modes(device, surface_khr)
                .unwrap()
        };

        SwapchainSupportDetails {
            capabilities,
            formats,
            present_modes,
        }
    }
}

// Getters implementation
impl VulkanPlatform {
    pub fn instance(&self) -> &Instance {
        &self.instance
    }

    pub fn surface(&self) -> &surface::Instance {
        &self.surface
    }

    pub fn surface_khr(&self) -> vk::SurfaceKHR {
        self.surface_khr
    }

    pub fn physical_device(&self) -> vk::PhysicalDevice {
        self.physical_device
    }

    pub fn device(&self) -> &Device {
        &self.device
    }

    pub fn graphics_queue(&self) -> vk::Queue {
        self.graphics_queue
    }

    pub fn present_queue(&self) -> vk::Queue {
        self.present_queue
    }

    pub fn context(&self) -> &VulkanContext {
        &self.context
    }

    pub fn queue_families_indices(&self) -> &QueueFamiliesIndices {
        &self.queue_families_indices
    }
}

impl Drop for VulkanPlatform {
    fn drop(&mut self) {
        debug!("Dropping Vulkan platform.");

        // 在销毁任何资源之前等待设备空闲
        // 这是确保所有GPU操作完成的关键步骤
        unsafe {
            // 首先等待设备空闲
            if let Err(err) = self.device.device_wait_idle() {
                debug!("Failed to wait for device to be idle: {:?}", err);
            }

            // 然后按照正确的顺序销毁资源
            self.device.destroy_device(None);
            self.surface.destroy_surface(self.surface_khr, None);
            self.instance.destroy_instance(None);
        }
    }
}
*/
