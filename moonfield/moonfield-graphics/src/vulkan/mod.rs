// Vulkan backend implementation - temporarily disabled during refactoring
/*
pub mod context;
pub mod driver;
pub mod driver_impl;
pub mod platform;
pub mod platform_impl;
pub mod swapchain;
pub mod swapchain_impl;
pub mod texture;
pub mod uniform_buffer;
pub mod vertex_buffer;

pub use context::VulkanContext;
pub use driver::VulkanDriver;
pub use driver_impl::VulkanDriverImpl;
pub use platform::VulkanPlatform;
pub use platform_impl::VulkanPlatformImpl;
pub use swapchain::Swapchain;
pub use swapchain_impl::SwapChain;
pub use texture::Texture;
pub use uniform_buffer::UniformBuffer;
pub use vertex_buffer::VertexBuffer;

pub mod prelude {
    pub use super::driver::*;
    pub use super::platform::*;
    pub use super::vertex_buffer::*;
    pub use crate::driver_enums::*;
}
*/
