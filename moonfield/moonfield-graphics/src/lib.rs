//! Graphics backend abstraction for the Moonfield engine.
//!
//! This crate provides a unified interface for different graphics APIs
//! including Vulkan, OpenGL, and WebGPU, as well as the main Engine.

pub mod vulkan;
pub mod driver_enums;
pub mod driver;
pub mod platform;
pub mod engine;

// Re-export commonly used types
pub use driver_enums::*;
pub use driver::{Driver, DriverFactory};
pub use platform::{Platform, PlatformFactory};
pub use engine::Engine;
