use crate::{Driver, DriverFactory, Platform, PlatformFactory, Backend};
use tracing::debug;

use std::sync::{Arc, Mutex};

/// Engine是Moonfield的核心，它是应用程序与渲染系统交互的主要接口
pub struct Engine {
    pub(crate) backend: Backend,
    pub(crate) driver: Arc<Mutex<Box<dyn Driver>>>,
    pub(crate) platform: Arc<Mutex<Box<dyn Platform>>>,
}

impl Engine {
    /// 创建一个新的Engine实例
    pub fn create<T: 'static>(backend: Backend, window: &T) -> Result<Self, String> {
        // 解析后端类型（如果是Default，则选择平台默认值）
        let resolved_backend = backend.resolve();

        // 创建平台
        let mut platform = PlatformFactory::create(resolved_backend, window)?;

        // 初始化平台
        platform.as_mut().init()?;

        // 创建驱动
        let mut driver = DriverFactory::create(resolved_backend)?;

        // 初始化驱动
        driver.as_mut().init()?;

        Ok(Self {
            backend: resolved_backend,
            driver: Arc::new(Mutex::new(driver)),
            platform: Arc::new(Mutex::new(platform)),
        })
    }

    /// 获取后端类型
    pub fn get_backend_type(&self) -> Backend {
        self.backend
    }

    /// 获取驱动API
    pub fn get_driver(&self) -> Arc<Mutex<Box<dyn Driver>>> {
        Arc::clone(&self.driver)
    }

    /// 获取平台
    pub fn get_platform(&self) -> Arc<Mutex<Box<dyn Platform>>> {
        Arc::clone(&self.platform)
    }
}

impl Drop for Engine {
    fn drop(&mut self) {
        // 确保在Engine被销毁时，所有资源都被正确清理
        debug!("Dropping Engine");

        // 使用try_lock来避免死锁
        if let Ok(mut driver) = self.driver.try_lock() {
            debug!("Cleaning up driver");
            driver.cleanup();
        } else {
            debug!("Could not lock driver for cleanup");
        }

        // 使用try_lock来避免死锁
        if let Ok(mut platform) = self.platform.try_lock() {
            debug!("Cleaning up platform");
            platform.cleanup();
        } else {
            debug!("Could not lock platform for cleanup");
        }
    }
}
