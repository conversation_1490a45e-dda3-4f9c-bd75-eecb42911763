//! Entity Component System for the Moonfield engine.
//!
//! This crate provides a high-performance ECS implementation with
//! structure-of-arrays storage and efficient entity management.

pub mod entity;
pub mod entity_manager;
pub mod structure_of_arrays;

// Re-export commonly used items
pub use entity::Entity;
pub use entity_manager::EntityManager;
pub use structure_of_arrays::{BundleDef, StructureOfArrays, TypeInfo};