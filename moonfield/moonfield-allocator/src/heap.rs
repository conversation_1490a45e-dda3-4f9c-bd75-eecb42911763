//! Heap memory allocator implementation.
//!
//! This module provides a heap allocator that uses the system allocator
//! for memory management.

use super::{Allocator, AllocatorError, AllocatorStats, AllocatorStatsProvider};
use std::alloc::{Layout, alloc, dealloc};
use std::ptr::NonNull;

/// A heap allocator that uses the system allocator for memory management.
///
/// Features:
/// - Uses system allocator for all allocations
/// - Frees individual blocks
/// - Tracks allocation statistics
pub struct HeapAllocator {
    total_allocated: std::sync::atomic::AtomicUsize,
    peak_allocated: std::sync::atomic::AtomicUsize,
    allocation_count: std::sync::atomic::AtomicUsize,
    failed_allocations: std::sync::atomic::AtomicUsize,
}

impl HeapAllocator {
    /// Create a new heap allocator.
    pub fn new() -> Self {
        Self {
            total_allocated: std::sync::atomic::AtomicUsize::new(0),
            peak_allocated: std::sync::atomic::AtomicUsize::new(0),
            allocation_count: std::sync::atomic::AtomicUsize::new(0),
            failed_allocations: std::sync::atomic::AtomicUsize::new(0),
        }
    }

    /// Allocate memory with the specified size and alignment.
    ///
    /// # Returns
    ///
    /// A pointer to the allocated memory, or `None` if allocation failed.
    pub fn aligned_alloc(&mut self, size: usize, alignment: usize) -> Option<*mut u8> {
        if size == 0 {
            return Some(alignment as *mut u8);
        }

        let layout = Layout::from_size_align(size, alignment).ok()?;
        let ptr = unsafe { alloc(layout) };
        let ptr = NonNull::new(ptr)?;
        
        // Update statistics
        self.allocation_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        let current = self.total_allocated.fetch_add(size, std::sync::atomic::Ordering::Relaxed);
        let peak = self.peak_allocated.load(std::sync::atomic::Ordering::Relaxed);
        if current + size > peak {
            self.peak_allocated.store(current + size, std::sync::atomic::Ordering::Relaxed);
        }
        
        Some(ptr.as_ptr())
    }

    /// Free memory previously allocated by this allocator.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it deallocates memory.
    /// The caller must ensure that the pointer was allocated by this allocator
    /// and that it is not used after being freed.
    pub unsafe fn aligned_free(&mut self, ptr: *mut u8, size: usize, alignment: usize) {
        if ptr.is_null() || size == 0 {
            return;
        }

        let layout = unsafe { Layout::from_size_align_unchecked(size, alignment) };
        unsafe {
            dealloc(ptr, layout);
        }
        
        // Update statistics
        self.total_allocated.fetch_sub(size, std::sync::atomic::Ordering::Relaxed);
    }

    /// Get the total amount of memory currently allocated.
    pub fn total_allocated(&self) -> usize {
        self.total_allocated.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// Get the peak amount of memory that was allocated.
    pub fn peak_allocated(&self) -> usize {
        self.peak_allocated.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// Get the number of active allocations.
    pub fn allocation_count(&self) -> usize {
        self.allocation_count.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// Get the number of failed allocations.
    pub fn failed_allocations(&self) -> usize {
        self.failed_allocations.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// Reset the peak allocation counter.
    pub fn reset_peak(&mut self) {
        let current = self.total_allocated.load(std::sync::atomic::Ordering::Relaxed);
        self.peak_allocated.store(current, std::sync::atomic::Ordering::Relaxed);
    }

    /// Record a failed allocation attempt.
    pub fn record_failed_allocation(&mut self) {
        self.failed_allocations.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
    }
}

impl Default for HeapAllocator {
    fn default() -> Self {
        Self::new()
    }
}

impl Clone for HeapAllocator {
    fn clone(&self) -> Self {
        Self {
            total_allocated: std::sync::atomic::AtomicUsize::new(self.total_allocated.load(std::sync::atomic::Ordering::Relaxed)),
            peak_allocated: std::sync::atomic::AtomicUsize::new(self.peak_allocated.load(std::sync::atomic::Ordering::Relaxed)),
            allocation_count: std::sync::atomic::AtomicUsize::new(self.allocation_count.load(std::sync::atomic::Ordering::Relaxed)),
            failed_allocations: std::sync::atomic::AtomicUsize::new(self.failed_allocations.load(std::sync::atomic::Ordering::Relaxed)),
        }
    }
}

impl Allocator for HeapAllocator {
    fn alloc(&mut self, size: usize, alignment: usize, _offset: usize) -> Option<*mut u8> {
        let result = self.aligned_alloc(size, alignment);
        if result.is_none() {
            self.record_failed_allocation();
        }
        result
    }

    unsafe fn free(&mut self, ptr: *mut u8, size: usize) {
        // Use a reasonable default alignment if we don't know the exact one
        unsafe {
            self.aligned_free(ptr, size, 8);
        }
    }

    fn total_size(&self) -> usize {
        // Heap allocator doesn't have a fixed size
        usize::MAX
    }

    fn allocated_size(&self) -> usize {
        self.total_allocated()
    }

    fn try_alloc_with_context(&mut self, size: usize, alignment: usize, context: &str) -> Result<*mut u8, crate::AllocatorError> {
        if size == 0 {
            return Err(crate::AllocatorError::ZeroSize);
        }
        if !alignment.is_power_of_two() {
            return Err(crate::AllocatorError::InvalidAlignment { alignment });
        }
        
        match self.aligned_alloc(size, alignment) {
            Some(ptr) => Ok(ptr),
            None => {
                self.record_failed_allocation();
                Err(crate::AllocatorError::Internal {
                    message: format!("Heap allocation failed for {} bytes with alignment {}: {}", size, alignment, context)
                })
            }
        }
    }
}

impl AllocatorStatsProvider for HeapAllocator {
    fn stats(&self) -> AllocatorStats {
        AllocatorStats {
            total_size: self.total_size(),
            allocated_size: self.allocated_size(),
            available_size: self.available_size(),
            peak_allocated: self.peak_allocated(),
            allocation_count: self.allocation_count(),
            failed_allocations: self.failed_allocations(),
        }
    }

    fn reset_stats(&mut self) {
        self.total_allocated.store(0, std::sync::atomic::Ordering::Relaxed);
        self.peak_allocated.store(0, std::sync::atomic::Ordering::Relaxed);
        self.allocation_count.store(0, std::sync::atomic::Ordering::Relaxed);
        self.failed_allocations.store(0, std::sync::atomic::Ordering::Relaxed);
    }
}

#[cfg(test)]
mod tests {
    use super::*; // Import everything from the parent module, including HeapAllocator
    use std::slice;
    use std::error::Error;

    #[test]
    fn test_alloc_and_free_happy_path() {
        // Test the basic allocation and deallocation flow.
        let mut allocator = HeapAllocator::new();
        let size = 128;
        let alignment = 8;

        // 1. Allocate memory.
        let ptr_option = allocator.aligned_alloc(size, alignment);
        assert!(ptr_option.is_some(), "Allocation should succeed");
        let ptr = ptr_option.unwrap();
        assert!(!ptr.is_null(), "The returned pointer should not be null");

        // 2. Verify that we can safely write to and read from the memory.
        unsafe {
            // Create a slice from the raw pointer to safely operate on the memory.
            let slice = slice::from_raw_parts_mut(ptr, size);
            slice.fill(0xAB); // Write data.
            assert_eq!(slice[0], 0xAB);
            assert_eq!(slice[size - 1], 0xAB);
        }

        // 3. Deallocate the memory.
        unsafe {
            allocator.aligned_free(ptr, size, alignment);
        }
        // If the test runs to this point without crashing, it implies the free
        // operation did not cause an immediate memory error.
    }

    #[test]
    fn test_alignment_check() {
        // Verify that the allocated pointer satisfies the alignment requirement.
        let mut allocator = HeapAllocator::new();
        let size = 256;
        let alignment = 64; // Use a larger alignment than the default.

        let ptr = allocator
            .aligned_alloc(size, alignment)
            .expect("Allocation failed");
        assert!(!ptr.is_null());

        // Core check: the pointer's address must be a multiple of the alignment.
        let addr = ptr as usize;
        assert_eq!(
            addr % alignment,
            0,
            "Pointer address {} is not aligned to {}",
            addr,
            alignment
        );

        unsafe {
            allocator.aligned_free(ptr, size, alignment);
        }
    }

    #[test]
    fn test_alloc_zero_size() {
        // Test the special behavior for zero-sized allocations.
        let mut allocator = HeapAllocator::new();
        let alignment = 8;

        // According to the implementation, a zero-sized allocation returns a pointer equal to the alignment.
        let ptr_option = allocator.aligned_alloc(0, alignment);
        assert!(ptr_option.is_some());
        // Note: This is a special, non-dereferenceable pointer.
        assert_eq!(ptr_option.unwrap() as usize, alignment);

        // Deallocating a zero-sized allocation should be a harmless no-op.
        unsafe {
            allocator.aligned_free(ptr_option.unwrap(), 0, alignment);
        }
    }

    #[test]
    fn test_free_null_or_zero_size() {
        // Test the safety of deallocating an invalid pointer or a zero-sized block.
        let mut allocator = HeapAllocator::new();

        // Deallocating a null pointer should not cause a panic.
        unsafe {
            allocator.aligned_free(std::ptr::null_mut(), 100, 8);
        }

        // Allocate a real block, then try to free it with size=0. This should also not panic.
        let size = 32;
        let alignment = 8;
        let ptr = allocator.aligned_alloc(size, alignment).unwrap();
        unsafe {
            allocator.aligned_free(ptr, 0, alignment);
        }

        // We still need to free it properly with the correct size to avoid a memory leak in the test.
        unsafe {
            allocator.aligned_free(ptr, size, alignment);
        }
    }

    #[test]
    fn test_invalid_alignment_fails() {
        // Test that allocation fails as expected when the alignment is not a power of two.
        let mut allocator = HeapAllocator::new();
        let size = 100;
        let invalid_alignment = 7; // Not a power of two.

        // Layout::from_size_align should fail, so aligned_alloc should return None.
        let result = allocator.aligned_alloc(size, invalid_alignment);
        assert!(
            result.is_none(),
            "Allocation with invalid alignment should fail and return None"
        );
    }

    #[test]
    fn test_allocator_trait_impl() {
        // Test if the implementation of the Allocator trait works correctly.
        let mut allocator: Box<dyn Allocator> = Box::new(HeapAllocator::new());
        let size = 64;
        let alignment = 16;
        let offset = 0; // HeapAllocator ignores the offset.

        let ptr = allocator
            .alloc(size, alignment, offset)
            .expect("alloc failed");
        assert!(!ptr.is_null());
        assert_eq!((ptr as usize) % alignment, 0);

        unsafe {
            // The `free` method in the trait impl uses a default alignment of 8.
            // Note: This could be problematic in practice, but we are testing the behavior as written.
            allocator.free(ptr, size);
        }
    }

    #[test]
    fn test_try_alloc_with_errors() {
        let mut allocator = HeapAllocator::new();

        // Test zero size error
        let result = allocator.try_alloc(0, 8, 0);
        assert!(matches!(result, Err(AllocatorError::ZeroSize)));

        // Test invalid alignment error
        let result = allocator.try_alloc(100, 7, 0);
        assert!(matches!(result, Err(AllocatorError::InvalidAlignment { alignment: 7 })));

        // Test successful allocation
        let result = allocator.try_alloc(100, 8, 0);
        assert!(result.is_ok());
        let ptr = result.unwrap();
        assert!(!ptr.is_null());

        unsafe {
            allocator.free(ptr, 100);
        }
    }

    #[test]
    fn test_try_free_with_errors() {
        let mut allocator = HeapAllocator::new();

        // Test null pointer error
        let result = unsafe { allocator.try_free(std::ptr::null_mut(), 100) };
        assert!(matches!(result, Err(AllocatorError::InvalidPointer { ptr: _ })));

        // Test zero size error
        let ptr = allocator.alloc(100, 8, 0).unwrap();
        let result = unsafe { allocator.try_free(ptr, 0) };
        assert!(matches!(result, Err(AllocatorError::ZeroSize)));

        // Test successful free
        let result = unsafe { allocator.try_free(ptr, 100) };
        assert!(result.is_ok());
    }

    #[test]
    fn test_statistics_tracking() {
        let mut allocator = HeapAllocator::new();

        // Initial state
        let stats = allocator.stats();
        assert_eq!(stats.allocated_size, 0);
        assert_eq!(stats.allocation_count, 0);
        assert_eq!(stats.failed_allocations, 0);

        // Allocate some memory
        let ptr1 = allocator.alloc(100, 8, 0).unwrap();
        let ptr2 = allocator.alloc(200, 16, 0).unwrap();

        let stats = allocator.stats();
        assert_eq!(stats.allocated_size, 300);
        assert_eq!(stats.allocation_count, 2);
        assert_eq!(stats.failed_allocations, 0);
        assert_eq!(stats.peak_allocated, 300);

        // Free some memory
        unsafe {
            allocator.free(ptr1, 100);
        }

        let stats = allocator.stats();
        assert_eq!(stats.allocated_size, 200);
        assert_eq!(stats.allocation_count, 2);
        assert_eq!(stats.peak_allocated, 300); // Peak should remain

        // Test failed allocation
        let _ = allocator.alloc(usize::MAX, 8, 0); // This should fail
        let stats = allocator.stats();
        assert_eq!(stats.failed_allocations, 1);

        // Clean up
        unsafe {
            allocator.free(ptr2, 200);
        }
    }

    #[test]
    fn test_reset_stats() {
        let mut allocator = HeapAllocator::new();

        // Allocate some memory
        let ptr = allocator.alloc(100, 8, 0).unwrap();
        let _ = allocator.alloc(usize::MAX, 8, 0); // Failed allocation

        // Verify stats are non-zero
        let stats = allocator.stats();
        assert!(stats.allocated_size > 0);
        assert!(stats.allocation_count > 0);
        assert!(stats.failed_allocations > 0);

        // Reset stats
        allocator.reset_stats();

        // Verify stats are reset
        let stats = allocator.stats();
        assert_eq!(stats.allocated_size, 0);
        assert_eq!(stats.allocation_count, 0);
        assert_eq!(stats.failed_allocations, 0);

        // Clean up
        unsafe {
            allocator.free(ptr, 100);
        }
    }

    #[test]
    fn test_pointer_math_utilities() {
        use super::super::pointermath;

        let mut buffer = [0u8; 1024];
        let ptr = buffer.as_mut_ptr();

        // Test alignment check
        assert!(pointermath::is_aligned_mut(ptr, 1));
        assert!(pointermath::is_aligned_mut(ptr, 2));
        assert!(pointermath::is_aligned_mut(ptr, 4));
        assert!(pointermath::is_aligned_mut(ptr, 8));

        // Test alignment offset
        let offset = pointermath::alignment_offset_mut(ptr, 64);
        assert!(offset < 64);
        assert_eq!((ptr as usize + offset) % 64, 0);

        // Test aligned pointer
        let aligned_ptr = unsafe { pointermath::align_mut(ptr, 64) };
        assert_eq!((aligned_ptr as usize) % 64, 0);
    }

    #[test]
    fn test_thiserror_functionality() {
        let mut allocator = HeapAllocator::new();

        // Test error display
        let zero_size_error = AllocatorError::ZeroSize;
        assert_eq!(zero_size_error.to_string(), "Allocation size cannot be zero");

        let invalid_alignment_error = AllocatorError::InvalidAlignment { alignment: 7 };
        assert_eq!(invalid_alignment_error.to_string(), "Invalid alignment (must be power of 2): 7");

        let out_of_memory_error = AllocatorError::OutOfMemory;
        assert_eq!(out_of_memory_error.to_string(), "Allocator out of memory");

        let invalid_pointer_error = AllocatorError::InvalidPointer { ptr: std::ptr::null_mut::<u8>() as usize };
        assert!(invalid_pointer_error.to_string().starts_with("Invalid pointer"));

        let internal_error = AllocatorError::Internal {
            message: "Test error message".to_string()
        };
        assert_eq!(internal_error.to_string(), "Internal allocator error: Test error message");

        // Test error source (thiserror automatically implements Error trait)
        assert!(internal_error.source().is_none());
    }

    #[test]
    fn test_try_alloc_with_context() {
        let mut allocator = HeapAllocator::new();

        // Test successful allocation with context
        let result = allocator.try_alloc_with_context(100, 8, "test allocation");
        assert!(result.is_ok());
        let ptr = result.unwrap();
        assert!(!ptr.is_null());

        // Test zero size error with context
        let result = allocator.try_alloc_with_context(0, 8, "zero size test");
        assert!(matches!(result, Err(AllocatorError::ZeroSize)));

        // Test invalid alignment error with context
        let result = allocator.try_alloc_with_context(100, 7, "invalid alignment test");
        assert!(matches!(result, Err(AllocatorError::InvalidAlignment { alignment: 7 })));

        // Test internal error with context (try to allocate a very large amount)
        let result = allocator.try_alloc_with_context(usize::MAX, 8, "huge allocation test");
        assert!(matches!(result, Err(AllocatorError::Internal { message: _ })));
        
        if let Err(AllocatorError::Internal { message }) = result {
            assert!(message.contains("huge allocation test"));
            assert!(message.contains("Heap allocation failed"));
        }

        // Clean up
        unsafe {
            allocator.free(ptr, 100);
        }
    }
}
