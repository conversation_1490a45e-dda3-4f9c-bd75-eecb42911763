//! Memory allocation utilities for the Moonfield engine.
//!
//! This crate provides various memory allocators and allocation utilities:
//! - `LinearAllocator`: Allocates blocks linearly, cannot free individual blocks
//! - `HeapAllocator`: Uses system allocator for memory management
//! - `PoolAllocator`: Allocates fixed-size blocks from a memory pool
//! - `FreeList`: Manages a list of free memory blocks
//! - Various tracking and area policies

use std::collections::HashMap;
use std::ptr::NonNull;
use thiserror::Error;
use futures::FutureExt;

pub mod area;
pub mod freelist;
pub mod heap;
pub mod linear;
pub mod pool;
pub mod tracking;
pub mod arena;

// Re-export commonly used types
pub use area::{Area, HeapArea};
pub use linear::LinearAllocator;
pub use pool::ObjectPoolAllocator;
pub use heap::HeapAllocator;

/// Custom error types for allocator operations.
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Error)]
pub enum AllocatorError {
    /// The allocator is out of memory
    #[error("Allocator out of memory")]
    OutOfMemory,
    /// Invalid alignment (not a power of 2)
    #[error("Invalid alignment (must be power of 2): {alignment}")]
    InvalidAlignment { alignment: usize },
    /// Invalid pointer (null or out of bounds)
    #[error("Invalid pointer: {ptr:#x}")]
    InvalidPointer { ptr: usize },
    /// Size is zero
    #[error("Allocation size cannot be zero")]
    ZeroSize,
    /// Requested size is too large
    #[error("Requested size {size} is too large (max: {max_size})")]
    SizeTooLarge { size: usize, max_size: usize },
    /// Memory corruption detected
    #[error("Memory corruption detected: {details}")]
    MemoryCorruption { details: String },
    /// Internal allocator error
    #[error("Internal allocator error: {message}")]
    Internal { message: String },
    /// Thread safety violation
    #[error("Thread safety violation: {details}")]
    ThreadSafetyViolation { details: String },
}

impl AllocatorError {
    /// Create an invalid alignment error with context.
    pub fn invalid_alignment(alignment: usize) -> Self {
        Self::InvalidAlignment { alignment }
    }
    
    /// Create an invalid pointer error with context.
    pub fn invalid_pointer(ptr: *mut u8) -> Self {
        Self::InvalidPointer { ptr: ptr as usize }
    }
    
    /// Create a size too large error with context.
    pub fn size_too_large(size: usize, max_size: usize) -> Self {
        Self::SizeTooLarge { size, max_size }
    }
    
    /// Create a memory corruption error with context.
    pub fn memory_corruption(details: impl Into<String>) -> Self {
        Self::MemoryCorruption { details: details.into() }
    }
    
    /// Create a thread safety violation error with context.
    pub fn thread_safety_violation(details: impl Into<String>) -> Self {
        Self::ThreadSafetyViolation { details: details.into() }
    }
}

/// Statistics about memory allocation.
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct AllocatorStats {
    /// Total size of the allocator's memory area
    pub total_size: usize,
    /// Amount of memory currently allocated
    pub allocated_size: usize,
    /// Amount of memory still available
    pub available_size: usize,
    /// Peak amount of memory that was allocated
    pub peak_allocated: usize,
    /// Number of active allocations
    pub allocation_count: usize,
    /// Number of failed allocations
    pub failed_allocations: usize,
}

impl AllocatorStats {
    /// Create new statistics with default values.
    pub fn new() -> Self {
        Self {
            total_size: 0,
            allocated_size: 0,
            available_size: 0,
            peak_allocated: 0,
            allocation_count: 0,
            failed_allocations: 0,
        }
    }

    /// Calculate the utilization percentage.
    pub fn utilization_percentage(&self) -> f64 {
        if self.total_size == 0 {
            0.0
        } else {
            (self.allocated_size as f64 / self.total_size as f64) * 100.0
        }
    }

    /// Calculate the peak utilization percentage.
    pub fn peak_utilization_percentage(&self) -> f64 {
        if self.total_size == 0 {
            0.0
        } else {
            (self.peak_allocated as f64 / self.total_size as f64) * 100.0
        }
    }
}

impl Default for AllocatorStats {
    fn default() -> Self {
        Self::new()
    }
}

/// Trait for allocators that can provide statistics.
pub trait AllocatorStatsProvider {
    /// Get statistics about the allocator.
    fn stats(&self) -> AllocatorStats;
    
    /// Reset the statistics.
    fn reset_stats(&mut self);
}

/// Trait for allocators that can detect memory leaks.
pub trait LeakDetector {
    /// Check if there are any memory leaks.
    fn has_leaks(&self) -> bool;
    
    /// Get information about potential memory leaks.
    fn leak_info(&self) -> Option<String>;
    
    /// Reset leak detection state.
    fn reset_leak_detection(&mut self);
}

/// A simple leak detector that tracks allocations.
pub struct SimpleLeakDetector {
    active_allocations: HashMap<*mut u8, (usize, String)>,
    enabled: bool,
}

impl SimpleLeakDetector {
    /// Create a new leak detector.
    pub fn new() -> Self {
        Self {
            active_allocations: HashMap::new(),
            enabled: true,
        }
    }

    /// Enable or disable leak detection.
    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }

    /// Record an allocation.
    pub fn record_allocation(&mut self, ptr: *mut u8, size: usize, context: String) {
        if self.enabled {
            self.active_allocations.insert(ptr, (size, context));
        }
    }

    /// Record a deallocation.
    pub fn record_deallocation(&mut self, ptr: *mut u8) {
        if self.enabled {
            self.active_allocations.remove(&ptr);
        }
    }
}

impl Default for SimpleLeakDetector {
    fn default() -> Self {
        Self::new()
    }
}

impl LeakDetector for SimpleLeakDetector {
    fn has_leaks(&self) -> bool {
        !self.active_allocations.is_empty()
    }

    fn leak_info(&self) -> Option<String> {
        if self.active_allocations.is_empty() {
            None
        } else {
            let mut info = format!("Found {} memory leaks:\n", self.active_allocations.len());
            let total_size: usize = self.active_allocations.values().map(|(size, _)| size).sum();
            info.push_str(&format!("Total leaked memory: {} bytes\n", total_size));
            
            for (ptr, (size, context)) in &self.active_allocations {
                info.push_str(&format!("  {:p}: {} bytes ({})\n", ptr, size, context));
            }
            
            Some(info)
        }
    }

    fn reset_leak_detection(&mut self) {
        self.active_allocations.clear();
    }
}

/// Pointer math utilities for working with raw pointers.
pub mod pointermath {

    /// Add an offset to a mutable pointer.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it performs raw pointer arithmetic.
    /// The caller must ensure that the resulting pointer is valid.
    #[inline]
    pub unsafe fn add_mut<P, T>(a: *mut P, b: T) -> *mut P
    where
        T: Into<usize>,
    {
        (a as usize + b.into()) as *mut P
    }

    /// Align a mutable pointer to the specified alignment.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it performs raw pointer arithmetic.
    /// The caller must ensure that the resulting pointer is valid.
    #[inline]
    pub unsafe fn align_mut<P>(p: *mut P, alignment: usize) -> *mut P {
        // if alignment is power of two, then alignment - 1 will be all 1s,
        // and p + alignment - 1 will not overflow  
        debug_assert!(alignment > 0 && alignment.is_power_of_two());
        // if p is already aligned, then alignment -1 will ensure it will not 
        // overflow to next alignment block
        // use ! to clear lower bits, to align to the nearest alignment block
        ((p as usize + alignment - 1) & !(alignment - 1)) as *mut P
    }

    /// Align a mutable pointer to the specified alignment with an offset.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it performs raw pointer arithmetic.
    /// The caller must ensure that the resulting pointer is valid.
    #[inline]
    pub unsafe fn align_with_offset_mut<P>(p: *mut P, alignment: usize, offset: usize) -> *mut P {
        let p_offset = unsafe { add_mut(p, offset) };
        let result = unsafe { align_mut(p_offset, alignment) };
        debug_assert!(result >= p_offset);
        result
    }

    /// Check if a pointer is aligned to the specified alignment.
    #[inline]
    pub fn is_aligned<P>(ptr: *const P, alignment: usize) -> bool {
        debug_assert!(alignment > 0 && alignment.is_power_of_two());
        (ptr as usize) % alignment == 0
    }

    /// Check if a pointer is aligned to the specified alignment.
    #[inline]
    pub fn is_aligned_mut<P>(ptr: *mut P, alignment: usize) -> bool {
        is_aligned(ptr, alignment)
    }

    /// Calculate the alignment offset needed for a pointer.
    #[inline]
    pub fn alignment_offset<P>(ptr: *const P, alignment: usize) -> usize {
        debug_assert!(alignment > 0 && alignment.is_power_of_two());
        (alignment - (ptr as usize) % alignment) % alignment
    }

    /// Calculate the alignment offset needed for a mutable pointer.
    #[inline]
    pub fn alignment_offset_mut<P>(ptr: *mut P, alignment: usize) -> usize {
        alignment_offset(ptr, alignment)
    }
}

/// Trait for memory allocators.
pub trait Allocator {
    /// Allocate memory with the specified size and alignment.
    ///
    /// # Arguments
    ///
    /// * `size` - The size of the allocation in bytes.
    /// * `alignment` - The alignment of the allocation.
    /// * `offset` - Optional offset for the alignment.
    ///
    /// # Returns
    ///
    /// A pointer to the allocated memory, or `None` if allocation failed.
    fn alloc(&mut self, size: usize, alignment: usize, offset: usize) -> Option<*mut u8>;

    /// Allocate memory with the specified size and alignment.
    ///
    /// # Arguments
    ///
    /// * `size` - The size of the allocation in bytes.
    /// * `alignment` - The alignment of the allocation.
    ///
    /// # Returns
    ///
    /// A pointer to the allocated memory, or `None` if allocation failed.
    fn alloc_aligned(&mut self, size: usize, alignment: usize) -> Option<*mut u8> {
        self.alloc(size, alignment, 0)
    }

    /// Allocate memory with error information.
    ///
    /// # Arguments
    ///
    /// * `size` - The size of the allocation in bytes.
    /// * `alignment` - The alignment of the allocation.
    /// * `offset` - Optional offset for the alignment.
    ///
    /// # Returns
    ///
    /// A pointer to the allocated memory, or an error if allocation failed.
    fn try_alloc(&mut self, size: usize, alignment: usize, offset: usize) -> Result<*mut u8, AllocatorError> {
        if size == 0 {
            return Err(AllocatorError::ZeroSize);
        }
        if !alignment.is_power_of_two() {
            return Err(AllocatorError::InvalidAlignment { alignment });
        }
        
        self.alloc(size, alignment, offset)
            .ok_or(AllocatorError::OutOfMemory)
    }

    /// Allocate memory with error information (no offset).
    ///
    /// # Arguments
    ///
    /// * `size` - The size of the allocation in bytes.
    /// * `alignment` - The alignment of the allocation.
    ///
    /// # Returns
    ///
    /// A pointer to the allocated memory, or an error if allocation failed.
    fn try_alloc_aligned(&mut self, size: usize, alignment: usize) -> Result<*mut u8, AllocatorError> {
        self.try_alloc(size, alignment, 0)
    }

    /// Allocate memory with detailed error context.
    ///
    /// # Arguments
    ///
    /// * `size` - The size of the allocation in bytes.
    /// * `alignment` - The alignment of the allocation.
    /// * `context` - Context information for debugging.
    ///
    /// # Returns
    ///
    /// A pointer to the allocated memory, or an error if allocation failed.
    fn try_alloc_with_context(&mut self, size: usize, alignment: usize, context: &str) -> Result<*mut u8, AllocatorError> {
        if size == 0 {
            return Err(AllocatorError::ZeroSize);
        }
        if !alignment.is_power_of_two() {
            return Err(AllocatorError::InvalidAlignment { alignment });
        }
        
        match self.alloc(size, alignment, 0) {
            Some(ptr) => Ok(ptr),
            None => Err(AllocatorError::Internal {
                message: format!("Failed to allocate {} bytes with alignment {}: {}", size, alignment, context)
            })
        }
    }

    /// Free memory previously allocated by this allocator.
    ///
    /// # Arguments
    ///
    /// * `ptr` - Pointer to the memory to free.
    /// * `size` - Size of the allocation.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it deallocates memory.
    /// The caller must ensure that the pointer was allocated by this allocator
    /// and that it is not used after being freed.
    unsafe fn free(&mut self, ptr: *mut u8, size: usize);

    /// Free memory with error checking.
    ///
    /// # Arguments
    ///
    /// * `ptr` - Pointer to the memory to free.
    /// * `size` - Size of the allocation.
    ///
    /// # Returns
    ///
    /// An error if the free operation failed.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it deallocates memory.
    /// The caller must ensure that the pointer was allocated by this allocator
    /// and that it is not used after being freed.
    unsafe fn try_free(&mut self, ptr: *mut u8, size: usize) -> Result<(), AllocatorError> {
        if ptr.is_null() {
            return Err(AllocatorError::InvalidPointer { ptr: 0 });
        }
        if size == 0 {
            return Err(AllocatorError::ZeroSize);
        }
        
        self.free(ptr, size);
        Ok(())
    }

    /// Get the total size of the allocator's memory area.
    fn total_size(&self) -> usize {
        0 // Default implementation for allocators that don't track size
    }

    /// Get the amount of memory currently allocated.
    fn allocated_size(&self) -> usize {
        0 // Default implementation for allocators that don't track usage
    }

    /// Get the amount of memory still available.
    fn available_size(&self) -> usize {
        self.total_size().saturating_sub(self.allocated_size())
    }
}

// Implementation of Allocator trait for RefCell<T: Allocator>
use std::cell::RefCell;
impl<T: Allocator> Allocator for RefCell<T> {
    fn alloc(&mut self, size: usize, alignment: usize, offset: usize) -> Option<*mut u8> {
        self.get_mut().alloc(size, alignment, offset)
    }
    unsafe fn free(&mut self, ptr: *mut u8, size: usize) {
        unsafe {
            self.get_mut().free(ptr, size)
        }
    }
}

/// A type-safe allocator trait that provides better safety guarantees.
pub trait TypedAllocator<T> {
    /// Allocate memory for a single instance of type T.
    fn alloc_typed(&mut self) -> Result<*mut T, AllocatorError>;
    
    /// Allocate memory for multiple instances of type T.
    fn alloc_typed_array(&mut self, count: usize) -> Result<*mut T, AllocatorError>;
    
    /// Free memory for a single instance of type T.
    unsafe fn free_typed(&mut self, ptr: *mut T);
    
    /// Free memory for multiple instances of type T.
    unsafe fn free_typed_array(&mut self, ptr: *mut T, count: usize);
}

/// A builder pattern for allocator configuration.
#[derive(Debug, Clone)]
pub struct AllocatorBuilder {
    size: Option<usize>,
    alignment: Option<usize>,
    name: Option<String>,
    enable_tracking: bool,
    enable_debug: bool,
}

impl AllocatorBuilder {
    pub fn new() -> Self {
        Self {
            size: None,
            alignment: None,
            name: None,
            enable_tracking: false,
            enable_debug: false,
        }
    }
    
    pub fn with_size(mut self, size: usize) -> Self {
        self.size = Some(size);
        self
    }
    
    pub fn with_alignment(mut self, alignment: usize) -> Self {
        self.alignment = Some(alignment);
        self
    }
    
    pub fn with_name(mut self, name: impl Into<String>) -> Self {
        self.name = Some(name.into());
        self
    }
    
    pub fn with_tracking(mut self, enable: bool) -> Self {
        self.enable_tracking = enable;
        self
    }
    
    pub fn with_debug(mut self, enable: bool) -> Self {
        self.enable_debug = enable;
        self
    }
    
    pub fn build_linear_allocator(self) -> Result<LinearAllocator<HeapArea>, AllocatorError> {
        let size = self.size.ok_or(AllocatorError::Internal {
            message: "Size is required for linear allocator".to_string()
        })?;
        
        let area = HeapArea::new(size);
        Ok(LinearAllocator::new(area))
    }
    
    pub fn build_pool_allocator<T>(self) -> Result<ObjectPoolAllocator<HeapArea, T>, AllocatorError> {
        let size = self.size.ok_or(AllocatorError::Internal {
            message: "Size is required for pool allocator".to_string()
        })?;
        
        let area = HeapArea::new(size);
        unsafe { Ok(ObjectPoolAllocator::new(area)) }
    }
}

impl Default for AllocatorBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// A smart pointer that automatically frees memory when dropped.
pub struct AllocatedPtr<T> {
    ptr: NonNull<T>,
    size: usize,
    allocator: Box<dyn Allocator>,
}

impl<T> AllocatedPtr<T> {
    /// Create a new allocated pointer.
    pub fn new(ptr: *mut T, size: usize, allocator: Box<dyn Allocator>) -> Option<Self> {
        NonNull::new(ptr).map(|ptr| Self {
            ptr,
            size,
            allocator,
        })
    }
    
    /// Get a mutable reference to the allocated memory.
    pub fn as_mut(&mut self) -> &mut T {
        unsafe { self.ptr.as_mut() }
    }
    
    /// Get an immutable reference to the allocated memory.
    pub fn as_ref(&self) -> &T {
        unsafe { self.ptr.as_ref() }
    }
    
    /// Get the raw pointer.
    pub fn as_ptr(&self) -> *mut T {
        self.ptr.as_ptr()
    }
    
    /// Get the size of the allocation.
    pub fn size(&self) -> usize {
        self.size
    }
}

impl<T> Drop for AllocatedPtr<T> {
    fn drop(&mut self) {
        unsafe {
            self.allocator.free(self.ptr.as_ptr() as *mut u8, self.size);
        }
    }
}

impl<T> std::ops::Deref for AllocatedPtr<T> {
    type Target = T;
    
    fn deref(&self) -> &Self::Target {
        self.as_ref()
    }
}

impl<T> std::ops::DerefMut for AllocatedPtr<T> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        self.as_mut()
    }
}

/// Extension trait for allocators to provide smart pointer functionality.
pub trait SmartAllocator: Allocator {
    /// Allocate a smart pointer for a single instance of type T.
    fn alloc_smart<T>(&mut self) -> Result<AllocatedPtr<T>, AllocatorError> {
        let size = std::mem::size_of::<T>();
        let alignment = std::mem::align_of::<T>();
        
        let ptr = self.try_alloc(size, alignment, 0)?;
        AllocatedPtr::new(ptr as *mut T, size, Box::new(HeapAllocator::new()))
            .ok_or(AllocatorError::Internal {
                message: "Failed to create smart pointer".to_string()
            })
    }
    
    /// Clone the allocator (if supported).
    fn clone_allocator(&self) -> Box<dyn Allocator> {
        // Default implementation - override in specific allocators
        Box::new(HeapAllocator::new())
    }
}

pub trait AsyncAllocator: Send + Sync {
    /// Asynchronously allocate memory.
    fn alloc_async(&self, size: usize, alignment: usize, offset: usize) -> impl std::future::Future<Output = Result<usize, AllocatorError>> + Send;
    
    /// Asynchronously free memory.
    fn free_async(&self, ptr: usize, size: usize) -> impl std::future::Future<Output = Result<(), AllocatorError>> + Send;
    
    /// Get the total size of the allocator's memory area.
    fn total_size(&self) -> usize;
    
    /// Get the amount of memory currently allocated.
    fn allocated_size(&self) -> usize;
}

pub struct AsyncAllocatorWrapper<A> {
    inner: std::sync::Arc<std::sync::Mutex<A>>,
}

impl<A: Allocator + Send + 'static> AsyncAllocator for AsyncAllocatorWrapper<A> {
    fn alloc_async(&self, size: usize, alignment: usize, offset: usize) -> impl std::future::Future<Output = Result<usize, AllocatorError>> + Send {
        let inner = self.inner.clone();
        tokio::task::spawn_blocking(move || {
            let mut guard = inner.lock().unwrap();
            guard.try_alloc(size, alignment, offset).map(|ptr| ptr as usize)
        })
        .map(|res| res.unwrap_or_else(|e| Err(AllocatorError::Internal { message: format!("Tokio join error: {e}") })))
    }

    fn free_async(&self, ptr: usize, size: usize) -> impl std::future::Future<Output = Result<(), AllocatorError>> + Send {
        let inner = self.inner.clone();
        tokio::task::spawn_blocking(move || {
            let mut guard = inner.lock().unwrap();
            unsafe {
                guard.try_free(ptr as *mut u8, size)
            }
        })
        .map(|res| res.unwrap_or_else(|e| Err(AllocatorError::Internal { message: format!("Tokio join error: {e}") })))
    }

    fn total_size(&self) -> usize {
        let inner = self.inner.lock().unwrap();
        inner.total_size()
    }

    fn allocated_size(&self) -> usize {
        let inner = self.inner.lock().unwrap();
        inner.allocated_size()
    }
}
