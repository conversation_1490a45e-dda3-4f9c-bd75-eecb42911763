//! freelist.rs
//! A direct Rust translation of the C++ FreeList and AtomicFreeList logic.

use std::ptr;
use std::sync::atomic::{AtomicU64, Ordering};

// Intrusive node, stored within the free blocks themselves.
#[repr(C)]
pub struct Node {
    next: *mut Node,
}

/// A non-thread-safe free list allocator.
pub struct FreeList {
    head: *mut Node,
    #[cfg(debug_assertions)]
    begin: *mut u8,
    #[cfg(debug_assertions)]
    end: *mut u8,
}

impl FreeList {
    /// Creates a new FreeList over a given memory area.
    ///
    /// # Safety
    /// The caller must ensure the memory area [begin, end) is valid and
    /// outlives the FreeList. The behavior is undefined if element_size or
    /// alignment are 0.
    pub unsafe fn new(
        begin: *mut u8,
        end: *mut u8,
        element_size: usize,
        alignment: usize,
        offset: usize,
    ) -> Self {
        let head = unsafe { Self::init(begin, end, element_size, alignment, offset) };
        Self {
            head,
            #[cfg(debug_assertions)]
            begin,
            #[cfg(debug_assertions)]
            end,
        }
    }

    /// Pops a node from the front of the list.
    pub fn pop(&mut self) -> *mut u8 {
        if self.head.is_null() {
            ptr::null_mut()
        } else {
            let head_node = self.head;
            self.head = unsafe { (*head_node).next };
            head_node as *mut u8
        }
    }

    /// Pushes a node to the front of the list.
    ///
    /// # Safety
    /// The caller must ensure the pointer `p` was previously allocated from this
    /// list and is not null.
    pub unsafe fn push(&mut self, p: *mut u8) {
        debug_assert!(!p.is_null());
        #[cfg(debug_assertions)]
        debug_assert!(p >= self.begin && p < self.end, "Pointer out of bounds");

        let new_head = p as *mut Node;
        unsafe {
            (*new_head).next = self.head;
        }
        self.head = new_head;
    }
    
    /// Gets the head of the free list without removing it.
    pub fn get_first(&self) -> *mut u8 {
        self.head as *mut u8
    }

    // Helper function to initialize the linked list of free nodes.
    unsafe fn init(
        begin: *mut u8,
        end: *mut u8,
        element_size: usize,
        alignment: usize,
        offset: usize,
    ) -> *mut Node {
        let align_offset = unsafe { begin.add(offset).align_offset(alignment) };
        let p = if align_offset == usize::MAX { ptr::null_mut() } else { 
            unsafe { begin.add(offset + align_offset) }
        };
        if p.is_null() || p >= end {
            return ptr::null_mut();
        }

        let head = p as *mut Node;

        let next_align_offset = unsafe { p.add(element_size).align_offset(alignment) };
        let next_p = if next_align_offset == usize::MAX { ptr::null_mut() } else { 
            unsafe { p.add(element_size).add(next_align_offset) }
        };

        if next_p.is_null() || next_p <= p {
            unsafe {
                (*head).next = ptr::null_mut();
            }
            return head;
        }

        let stride = next_p as usize - p as usize;
        let count = (end as usize - p as usize) / stride;

        let mut current = head;
        for _ in 1..count {
            let next_node = unsafe { (current as *mut u8).add(stride) } as *mut Node;
            unsafe {
                (*current).next = next_node;
            }
            current = next_node;
        }
        unsafe {
            (*current).next = ptr::null_mut();
        }

        head
    }
}

// Represents the atomic head pointer, including a tag to prevent the ABA problem.
#[derive(Copy, Clone)]
#[repr(C)]
struct HeadPtr {
    offset: i32,
    tag: u32,
}

/// A thread-safe, lock-free free list that solves the ABA problem.
pub struct AtomicFreeList {
    head: AtomicU64,
    storage: *mut u8,
}

// THIS IS THE CRITICAL FIX:
// We promise the compiler that this struct, despite containing a raw pointer,
// is safe to be sent and shared across threads because we manage concurrency
// internally with atomics.
unsafe impl Send for AtomicFreeList {}
unsafe impl Sync for AtomicFreeList {}

impl AtomicFreeList {
    /// Creates a new AtomicFreeList.
    /// # Safety: Same as FreeList::new.
    pub unsafe fn new(
        begin: *mut u8,
        end: *mut u8,
        element_size: usize,
        alignment: usize,
        offset: usize,
    ) -> Self {
        let align_offset = unsafe { begin.add(offset).align_offset(alignment) };
        let p = if align_offset == usize::MAX { ptr::null_mut() } else { 
            unsafe { begin.add(offset + align_offset) }
        };

        let storage = p;
        let mut head_ptr = HeadPtr { offset: -1, tag: 0 };

        if !p.is_null() && p < end {
            unsafe {
                FreeList::init(p, end, element_size, alignment, 0);
            }
            head_ptr.offset = (p as usize - storage as usize) as i32;
        }

        Self {
            head: AtomicU64::new(unsafe { std::mem::transmute(head_ptr) }),
            storage,
        }
    }

    /// Atomically pops a node from the front of the list.
    pub fn pop(&self) -> *mut u8 {
        let mut current_val = self.head.load(Ordering::Relaxed);

        loop {
            let current_head: HeadPtr = unsafe { std::mem::transmute(current_val) };
            if current_head.offset < 0 {
                return ptr::null_mut();
            }

            let p_node = unsafe { self.storage.add(current_head.offset as usize) } as *mut Node;
            let next_node_ptr = unsafe { (*p_node).next };

            let new_offset = if next_node_ptr.is_null() {
                -1
            } else {
                (next_node_ptr as usize - self.storage as usize) as i32
            };

            let new_head = HeadPtr {
                offset: new_offset,
                tag: current_head.tag.wrapping_add(1),
            };

            match self.head.compare_exchange_weak(
                current_val,
                unsafe { std::mem::transmute(new_head) },
                Ordering::Acquire,
                Ordering::Relaxed,
            ) {
                Ok(_) => return p_node as *mut u8,
                Err(new_val) => current_val = new_val,
            }
        }
    }

    /// Atomically pushes a node to the front of the list.
    /// # Safety: Same as FreeList::push.
    pub unsafe fn push(&self, p: *mut u8) {
        debug_assert!(!p.is_null());

        let node_offset = (p as usize - self.storage as usize) as i32;
        let mut current_val = self.head.load(Ordering::Relaxed);

        loop {
            let current_head: HeadPtr = unsafe { std::mem::transmute(current_val) };
            let new_head = HeadPtr {
                offset: node_offset,
                tag: current_head.tag.wrapping_add(1),
            };

            let p_node = p as *mut Node;
            unsafe {
                (*p_node).next = if current_head.offset < 0 {
                    ptr::null_mut()
                } else {
                    self.storage.add(current_head.offset as usize) as *mut Node
                };
            }

            match self.head.compare_exchange_weak(
                current_val,
                unsafe { std::mem::transmute(new_head) },
                Ordering::Release,
                Ordering::Relaxed,
            ) {
                Ok(_) => return,
                Err(new_val) => current_val = new_val,
            }
        }
    }
    
    /// Atomically gets the head of the free list without removing it.
    pub fn get_first(&self) -> *mut u8 {
        let current_head: HeadPtr = unsafe {
            std::mem::transmute(self.head.load(Ordering::Relaxed))
        };
        if current_head.offset < 0 {
            ptr::null_mut()
        } else {
            unsafe { self.storage.add(current_head.offset as usize) }
        }
    }
}


// ---------------------------------------------------------------------------------
// ---------------------------- UNIT TESTS -----------------------------------------
// ---------------------------------------------------------------------------------
#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashSet;
    use std::sync::Arc;
    use std::thread;

    const POOL_SIZE: usize = 4096;
    const ELEMENT_SIZE: usize = 32;
    const ALIGNMENT: usize = 16;
    const OFFSET: usize = 0;

    /// A wrapper for a raw pointer that we explicitly mark as safe to send across
    /// thread boundaries. This is a promise to the compiler that we will handle
    /// the pointer's lifecycle correctly and not cause data races.
    #[derive(PartialEq, Eq, Hash, Copy, Clone, Debug)]
    struct SendablePtr(*mut u8);
    unsafe impl Send for SendablePtr {}

    // --- FreeList Tests ---

    #[test]
    fn test_freelist_new_and_pop_all() {
        let mut buffer = [0u8; POOL_SIZE];
        let mut freelist = unsafe {
            FreeList::new(
                buffer.as_mut_ptr(),
                buffer.as_mut_ptr().add(POOL_SIZE),
                ELEMENT_SIZE,
                ALIGNMENT,
                OFFSET,
            )
        };

        let mut allocations = HashSet::new();
        let mut count = 0;
        while let Some(p) = non_null(freelist.pop()) {
            assert!(allocations.insert(p), "Duplicate pointer allocated!");
            count += 1;
        }

        let first_element_offset = buffer.as_ptr().align_offset(ALIGNMENT);
        let effective_pool_size = POOL_SIZE - first_element_offset;
        let expected_count = effective_pool_size / ELEMENT_SIZE;

        assert_eq!(count, expected_count, "Did not allocate the expected number of blocks.");
        assert!(freelist.pop().is_null(), "Expected null when pool is empty.");
    }

    #[test]
    fn test_freelist_lifo_behavior() {
        let mut buffer = [0u8; POOL_SIZE];
        let mut freelist = unsafe {
            FreeList::new(
                buffer.as_mut_ptr(),
                buffer.as_mut_ptr().add(POOL_SIZE),
                ELEMENT_SIZE,
                ALIGNMENT,
                OFFSET,
            )
        };

        let p1 = freelist.pop();
        assert!(!p1.is_null());
        let p2 = freelist.pop();
        assert!(!p2.is_null());
        assert_ne!(p1, p2);

        unsafe { freelist.push(p1); }

        let p3 = freelist.pop();
        assert!(!p3.is_null());
        assert_eq!(p1, p3, "FreeList did not exhibit LIFO behavior.");
    }

    #[test]
    fn test_freelist_alignment() {
        let mut buffer = [0u8; POOL_SIZE];
        let mut freelist = unsafe {
            FreeList::new(
                buffer.as_mut_ptr(),
                buffer.as_mut_ptr().add(POOL_SIZE),
                ELEMENT_SIZE,
                ALIGNMENT,
                OFFSET,
            )
        };

        while let Some(p) = non_null(freelist.pop()) {
            assert_eq!((p as usize) % ALIGNMENT, 0, "Pointer is not aligned correctly.");
        }
    }

    // --- AtomicFreeList Tests ---

    #[test]
    fn test_atomic_freelist_single_thread_pop_all() {
        let mut buffer = [0u8; POOL_SIZE];
        let freelist = unsafe {
            AtomicFreeList::new(
                buffer.as_mut_ptr(),
                buffer.as_mut_ptr().add(POOL_SIZE),
                ELEMENT_SIZE,
                ALIGNMENT,
                OFFSET,
            )
        };

        let mut allocations = HashSet::new();
        let mut count = 0;
        while let Some(p) = non_null(freelist.pop()) {
            assert!(allocations.insert(p), "Duplicate pointer allocated!");
            count += 1;
        }

        let first_element_offset = buffer.as_ptr().align_offset(ALIGNMENT);
        let effective_pool_size = POOL_SIZE - first_element_offset;
        let expected_count = effective_pool_size / ELEMENT_SIZE;

        assert_eq!(count, expected_count, "Did not allocate the expected number of blocks.");
        assert!(freelist.pop().is_null(), "Expected null when pool is empty.");
    }

    #[test]
    fn test_atomic_freelist_concurrent_allocations() {
        // CORRECT WAY: Allocate on the heap for a stable address.
        // We leak it in the test, which is fine, to get a 'static lifetime.
        let mut buffer = vec![0u8; POOL_SIZE].into_boxed_slice();
        let begin = buffer.as_mut_ptr();
        let end = unsafe { begin.add(POOL_SIZE) };

        let freelist = unsafe {
            AtomicFreeList::new(begin, end, ELEMENT_SIZE, ALIGNMENT, OFFSET)
        };

        let first_element_offset = (begin as *const u8).align_offset(ALIGNMENT);
        let effective_pool_size = POOL_SIZE - first_element_offset;
        let total_elements = effective_pool_size / ELEMENT_SIZE;

        let freelist = Arc::new(freelist);
        let mut handles = vec![];
        let num_threads = 4;

        for _ in 0..num_threads {
            let freelist_clone = Arc::clone(&freelist);
            handles.push(thread::spawn(move || {
                let mut local_allocs = Vec::new();
                for _ in 0..(total_elements / num_threads) {
                    if let Some(p) = non_null(freelist_clone.pop()) {
                        local_allocs.push(SendablePtr(p));
                    }
                }
                local_allocs
            }));
        }

        let mut all_allocations = Vec::new();
        for handle in handles {
            all_allocations.extend(handle.join().unwrap());
        }

        let unique_allocations: HashSet<_> = all_allocations.iter().cloned().collect();
        assert_eq!(
            all_allocations.len(),
            unique_allocations.len(),
            "Detected duplicate allocations in a concurrent environment!"
        );

        assert!(all_allocations.len() <= total_elements);
        println!("Concurrently allocated {} unique blocks out of {}.", all_allocations.len(), total_elements);
    }

    #[test]
    fn test_atomic_freelist_concurrent_churn() {
        // CORRECT WAY: Allocate on the heap.
        let mut buffer = vec![0u8; POOL_SIZE].into_boxed_slice();
        let begin = buffer.as_mut_ptr();
        let end = unsafe { begin.add(POOL_SIZE) };

        let freelist = Arc::new(unsafe {
            AtomicFreeList::new(begin, end, ELEMENT_SIZE, ALIGNMENT, OFFSET)
        });

        let mut handles = vec![];
        let num_threads = 8;
        let iterations_per_thread = 1000;

        for _ in 0..num_threads {
            let freelist_clone = Arc::clone(&freelist);
            handles.push(thread::spawn(move || {
                for _ in 0..iterations_per_thread {
                    if let Some(p) = non_null(freelist_clone.pop()) {
                        let ptr_wrapper = SendablePtr(p);
                        unsafe { freelist_clone.push(ptr_wrapper.0); }
                    } else {
                        thread::yield_now();
                    }
                }
            }));
        }

        for handle in handles {
            handle.join().unwrap();
        }

        let mut final_count = 0;
        while let Some(_) = non_null(freelist.pop()) {
            final_count += 1;
        }

        let first_element_offset = (begin as *const u8).align_offset(ALIGNMENT);
        let effective_pool_size = POOL_SIZE - first_element_offset;
        let total_elements = effective_pool_size / ELEMENT_SIZE;

        assert_eq!(final_count, total_elements, "Not all blocks were returned to the pool after churn test.");
        println!("Churn test completed successfully. All {} blocks accounted for.", final_count);
    }

    // Helper to convert a raw pointer to an Option for easier testing.
    fn non_null(p: *mut u8) -> Option<*mut u8> {
        if p.is_null() {
            None
        } else {
            Some(p)
        }
    }
}