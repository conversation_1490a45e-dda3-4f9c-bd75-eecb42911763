[package]
name = "moonfield-utils"
version = "0.1.0"
edition = "2021"
description = "Utilities for the Moonfield engine"
license = "MIT OR Apache-2.0"

[dependencies]
moonfield-graphics = { path = "../moonfield-graphics" }
moonfield-ecs = { path = "../moonfield-ecs" }
moonfield-job-system = { path = "../moonfield-job-system" }
moonfield-allocator = { path = "../moonfield-allocator" }
tracing = { workspace = true }
nalgebra-glm = { workspace = true }
tobj = { workspace = true }
anyhow = { workspace = true }

[features]
default = []
